__pycache__/
*.pyc
*.pyo
*.pyd
*.pytest_cache/
.mypy_cache/

# VCS and editor files
.git
.gitignore
.vscode/
.idea/
.DS_Store

# Virtual environments
.venv/
venv/
new_env/

# Local data and logs
*.log
*.sqlite3
*.db

# OS/filesystem noise
Thumbs.db

# Docker configs (optional to exclude from context)
docker-compose*.yml

# Build caches
build/
dist/
*.egg-info/

# Avoid copying large local data directories at build time
assets/
data_files/
# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
docs/
LICENSE

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
htmlcov

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Environment files (will be provided via docker-compose)
.env
.env.local
.env.*.local

# Build artifacts
build/
dist/
*.egg-info/

# Temporary files
tmp/
temp/ 