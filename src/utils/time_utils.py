'''
Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
Date: 2025-09-19 20:34:04
LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 90252194+<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com
LastEditTime: 2025-10-27 16:21:41
FilePath: /WA HU/src/utils/time_utils.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
"""
Time utility functions for the Wiz Aroma Food Delivery system.
"""

import datetime
import logging


def is_open_now() -> bool:
    """Check if the bot is currently open based on working hours."""
    try:
        # Properly get Ethiopian local time (EAT, UTC+3)
        # Get UTC time using the recommended non-deprecated approach
        utc_now = datetime.datetime.now(datetime.timezone.utc)

        # Add 3 hours to get Ethiopian time
        ethiopia_offset = datetime.timedelta(hours=3)
        ethiopia_now = utc_now + ethiopia_offset

        current_time = ethiopia_now.time()
        current_day = ethiopia_now.weekday()  # Monday is 0 and Sunday is 6

        # For debugging
        logging.info(f"UTC time: {utc_now}, Ethiopian time: {ethiopia_now}")
        logging.info(
            f"Current day in Ethiopia: {current_day}, Current time in Ethiopia: {current_time}"
        )

        # Define working hours in Ethiopian local time - DDU (Dire Dawa University Branch)
        # Customer facing times: Lunch (5:30-7:30) | Dinner (12:00-2:30)
        # 24hr format for configuration: Lunch (11:30-13:30) | Dinner (18:00-20:30)
        # Available all days of the week

        # DDU operates all days of the week with the same hours
        lunch_start = datetime.time(11, 30)  # 11:30 AM (5:30 in 12hr format)
        lunch_end = datetime.time(13, 30)    # 1:30 PM (7:30 in 12hr format)
        dinner_start = datetime.time(18, 0)  # 6:00 PM (12:00 in 12hr format)
        dinner_end = datetime.time(20, 30)   # 8:30 PM (2:30 in 12hr format)

        if (lunch_start <= current_time <= lunch_end) or \
           (dinner_start <= current_time <= dinner_end):
            logging.info(
                f"Within DDU working hours. Day: {current_day}, Time: {current_time}"
            )
            return True

        return False
    except Exception as e:
        logging.error(f"Error in is_open_now function: {e}")
        # In case of an error, default to open to avoid blocking users
        return True


def generate_order_number(user_id, order_count=None):
    """Generate unique order number in format USERID_YYMMDDHHMM_XXXX
    where:
    - USERID is the Telegram user ID
    - YYMMDDHHMM is the date and time (2-digit year, month, day, hour, minute)
    - XXXX is the incremental count for that user"""
    from src.data_models import user_order_counts

    # Get or initialize user's order count
    if order_count is None:
        user_order_counts[user_id] = user_order_counts.get(user_id, 0) + 1
        order_count = user_order_counts[user_id]

    # Get Ethiopian local time (EAT, UTC+3)
    utc_now = datetime.datetime.now(datetime.timezone.utc)
    ethiopia_offset = datetime.timedelta(hours=3)
    ethiopia_now = utc_now + ethiopia_offset

    # Format the current time in Ethiopian time
    date_time_str = ethiopia_now.strftime("%y%m%d%H%M")  # Using Ethiopian time

    # Format the order count to 4 digits
    count_str = str(order_count).zfill(4)

    # Combine all parts with user_id
    return f"{user_id}_{date_time_str}_{count_str}"


def get_ethiopian_time_now() -> datetime.datetime:
    """Get current Ethiopian local time (EAT, UTC+3)"""
    utc_now = datetime.datetime.now(datetime.timezone.utc)
    ethiopia_offset = datetime.timedelta(hours=3)
    return utc_now + ethiopia_offset


def format_ethiopian_time(dt: datetime.datetime = None) -> str:
    """Format datetime in Ethiopian local time as string"""
    if dt is None:
        dt = get_ethiopian_time_now()
    return dt.strftime('%Y-%m-%d %H:%M:%S')


